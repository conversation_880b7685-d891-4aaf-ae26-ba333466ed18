import json
import pandas as pd
import csv

def extract_assistant_responses(input_file, output_csv, output_json, max_count=300):
    """
    从角色扮演数据中提取assistant的回答
    """
    assistant_responses = []
    count = 0
    
    # 读取JSONL文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            if count >= max_count:
                break
                
            try:
                data = json.loads(line.strip())
                conversations = data.get('conversations', [])
                
                # 提取每个对话中assistant的回答
                for conv in conversations:
                    if count >= max_count:
                        break
                    if conv.get('role') == 'assistant':
                        assistant_responses.append({
                            'index': count + 1,
                            'source': 1,
                            'sentence': conv.get('content', '').strip()
                        })
                        count += 1
                        
            except json.JSONDecodeError:
                continue
    
    # 保存为CSV格式
    df = pd.DataFrame(assistant_responses)
    df.to_csv(output_csv, index=False, encoding='utf-8')
    
    # 保存为JSON格式
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(assistant_responses, f, ensure_ascii=False, indent=2)
    
    print(f"成功提取了 {len(assistant_responses)} 条assistant回答")
    print(f"CSV文件保存至: {output_csv}")
    print(f"JSON文件保存至: {output_json}")
    
    return assistant_responses

# 执行提取
if __name__ == "__main__":
    input_file = "/root/zjn/datasets/smoltalk-chinese/data/role-playing.jsonl"
    output_csv = "/root/zjn/datasets/smoltalk-chinese/smoltalk.csv"
    output_json = "/root/zjn/datasets/smoltalk-chinese/smoltalk.json"
    
    responses = extract_assistant_responses(input_file, output_csv, output_json, 300)
    
    # 显示前3条数据作为示例
    print("\n前3条数据示例:")
    for i, resp in enumerate(responses[:3]):
        print(f"Index: {resp['index']}")
        print(f"Source: {resp['source']}")
        print(f"Sentence: {resp['sentence'][:100]}...")
        print("-" * 50)