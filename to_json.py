import pandas as pd

# 1. 读取 Parquet 文件
file_path = "/root/zjn/datasets/smoltalk-chinese/data/role-playing.parquet"
df = pd.read_parquet(file_path)

# 2. 查看数据（可选）
print(df.head())

# 3. 保存为 JSON 文件（每行一个 JSON 对象，即 JSONL 格式）
df.to_json(
    "/root/zjn/datasets/smoltalk-chinese/data/role-playing.jsonl",
    orient="records",  # 每行是一个字典
    lines=True,        # 每条记录单独一行（JSONL）
    force_ascii=False, # 保留中文
    indent=None        # 不缩进，紧凑格式
)